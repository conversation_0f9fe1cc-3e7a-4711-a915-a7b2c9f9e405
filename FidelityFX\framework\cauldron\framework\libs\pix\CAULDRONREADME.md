# WinPixEventRuntime

## Current Version
1.0.240308001

## How to update
1. Download the nuget package from the webpage https://www.nuget.org/packages/WinPixEventRuntime
2. Rename its `.nupkg` extension into `.zip`
3. Unzip it
4. Replace the current header files by the ones in `Include/WinPixEventRuntime/`
5. Replace the current `WinPixEventRuntime.lib` and `WinPixEventRuntime.dll` files by the ones in `bin/x64/`
6. Update this `CAULDRONREADME.md` with the new version number!


# PixGpuCaptureLibrary

## Current Version
2403.08

## How to update
1. Download Dr. Pix from: https://devblogs.microsoft.com/pix/download/
2. Follow the installation process
3. Replace the current `WinPixGpuCapturer.dll` file by the ones in `C:/Program Files/Microsoft PIX/<version>/`
4. Update this `CAULDRONREADME.md` with the new version number!