# memoryallocator

## Current Version
D3D12MemoryAllocator:	Mar. 6, 2024 commit 0a9cbbbdb0da95be059f6c40a958188bfbd96b9e from amd D3D12MemoryAllocator github public repo - master branch
VKMemoryAllocator:		Apr. 3, 2024 commit 5677097bafb8477097c6e3354ce68b7a44fd01a4 from amd VKMemoryAllocator github public repo - master branch

## How to update
1. Download or clone latest D3D12MemoryAllocator and VKMemoryAllocator
1. Copy D3D12MemAlloc.cpp/h and vk_mem_alloc.h respectively into this folder
1. Update this `CAULDRONREADME.md` with the new version information!
