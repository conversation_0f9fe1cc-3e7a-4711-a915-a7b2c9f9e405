<!-- @page page_samples_index Samples -->

<h1><PERSON><PERSON></h1>

All of the FidelityFX SDK samples are written in C++ and use the [Cauldron](../../framework/cauldron) sample framework.

- [FidelityFX Blur 1.1](blur.md)
- [FidelityFX Breadcrumbs 1.0](breadcrumbs.md)
- [FidelityFX Brixelizer 1.0](brixelizer-gi.md)
- [FidelityFX Combined Adaptive Compute Ambient Occlusion 1.4](combined-adaptive-compute-ambient-occlusion.md)
- [FidelityFX Contrast Adaptive Sharpening 1.2](contrast-adaptive-sharpening.md)
- [FidelityFX Depth of Field 1.1](depth-of-field.md)
- [FidelityFX Hybrid Reflections 1.3](hybrid-reflections.md)
- [FidelityFX Hybrid Shadows 1.3](hybrid-shadows.md)
- [FidelityFX Lens 1.1](lens.md)
- [FidelityFX Luminance Preserving Mapper 1.4](luminance-preserving-mapper.md)
- [FidelityFX Parallel Sort 1.3](parallel-sort.md)
- [FidelityFX Single Pass Downsampler 2.2](single-pass-downsampler.md)
- [FidelityFX Stochastic Screen-Space Reflections 1.5](stochastic-screen-space-reflections.md)
- [FidelityFX Super Resolution 2.3.3 & 3.1.4](super-resolution.md)
- [FidelityFX Variable Shading 1.2](variable-shading.md)

<!-- - @subpage page_samples_combined-adaptive-compute-ambient-occlusion "Combined Adaptive Compute Ambient Occlusion" -->
<!-- - @subpage page_samples_contrast-adaptive-sharpening "Contrast Adaptive Sharpening" -->
<!-- - @subpage page_samples_luminance-preserving-mapper "Luminance Preserving Mapper" -->
<!-- - @subpage page_samples_parallel-sort "Parallel Sort" -->
<!-- - @subpage page_samples_single-pass-downsampler "Single Pass Downsampler" -->
<!-- - @subpage page_samples_stochastic-screen-space-reflections "Stochastic Screen Space Reflections" -->
<!-- - @subpage page_samples_super-resolution "Super Resolution" -->
<!-- - @subpage page_samples_variable-shading "Variable Shading" -->
<!-- - @subpage page_samples_blur "Blur" -->
<!-- - @subpage page_samples_depth-of-field "Depth of Field" -->
<!-- - @subpage page_samples_lens "Lens" -->
<!-- - @subpage page_samples_hybrid-reflections "Hybrid Reflections" -->
<!-- - @subpage page_samples_hybrid-shadows "Hybrid Shadows" -->
<!-- - @subpage page_samples_breadcrumbs "Breadcrumbs" -->
<!-- - @subpage page_samples_brixelizer "Brixelizer GI" -->
