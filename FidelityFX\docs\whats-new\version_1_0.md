<!-- @page page_whats-new_index_1_1.1 AMD FidelityFX SDK: What's new in FidelityFX SDK 1.1.1 -->

<h1>What's new in the FidelityFX SDK 1.1.1?</h1>

Welcome to the FidelityFX SDK. This inaugural version of the SDK features updated versions of all previously released FidelityFX samples ported to our new easy to use SDK format. Alongside those updated samples, we are pleased to announce the release of 3 new effects in the FidelityFX family.

<h2>New effects</h2>

None.

<h2>Updated effects</h2>

 All effects in this release have had their code updated with our latest optimizations and fixes, and have been ported to our new backing graphics framework.

<h2>Updated documentation</h2>

 With the introduction of the new FidelityFX SDK, all documentation for the constituent effects and samples has been refreshed and is available as easy to read Markdown.
 
 This change allows for a higher quality experience when exploring and reading the documentation, but also for suggested changes to be more easily submitted to our GitHub repository.

<h2>Deprecated effects</h2>

None.

<h2>Deprecated components</h2>

FidelityFX Cauldron Framework 1.x is now deprecated and has been replaced with a re-architected Cauldron 2, which now lives directly in the SDK under the [`framework`](../../framework) folder.

