// This file is part of the FidelityFX SDK.
//
// Copyright (C) 2024 Advanced Micro Devices, Inc.
// 
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files(the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and /or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions :
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

#pragma once
#include <mutex>

namespace cauldron
{

    /**
     * @class Sync
     *
     * Sync primitive class (CPU-side multithreading synchronization).
     *
     * @ingroup CauldronMisc
     */
    class Sync
    {
    public:

        /**
         * @brief   Increments the lock count on the sync primitive.
         */
        int Inc()
        {
            std::unique_lock<std::mutex> lock(m_Mutex);
            m_Count++;
            return m_Count;
        }

        /**
         * @brief   Decrements the lock count on the sync primitive.
         */
        int Dec()
        {
            std::unique_lock<std::mutex> lock(m_Mutex);
            m_Count--;
            if (m_Count == 0)
                m_condition.notify_all();
            return m_Count;
        }

        /**
         * @brief   Gets the lock count on the sync primitive.
         */
        int Get()
        {
            std::unique_lock<std::mutex> lock(m_Mutex);
            return m_Count;
        }

        /**
         * @brief   Resets the lock count on the sync primitive.
         */
        void Reset()
        {
            std::unique_lock<std::mutex> lock(m_Mutex);
            m_Count = 0;
            m_condition.notify_all();
        }

        /**
         * @brief   Waits until the lock count is 0 (or reset) on the sync primitive.
         */
        void Wait()
        {
            std::unique_lock<std::mutex> lock(m_Mutex);
            while (m_Count != 0)
                m_condition.wait(lock);
        }

    private:
        int32_t m_Count = 0;
        std::mutex m_Mutex;
        std::condition_variable m_condition;
    };

} // namespace cauldron
