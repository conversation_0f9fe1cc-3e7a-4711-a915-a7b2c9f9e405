<!-- @page page_building-samples_index Generating solutions for the FidelityFX SDK -->

<h1>Building FidelityFX SDK Samples</h1>

<h2>Generating solutions for the FidelityFX SDK</h2>

To build the samples in the AMD FidelityFX SDK:

 1. Download and install the following software developer tool minimum versions:
    - [CMake 3.17 - 3.30](https://cmake.org/download/)
    - [Visual Studio 2019](https://visualstudio.microsoft.com/downloads/)
    - [Windows 10 SDK 10.0.18362.0](https://developer.microsoft.com/en-us/windows/downloads/windows-10-sdk)
    - [Vulkan SDK 1.3.239](https://vulkan.lunarg.com/)

 2. Generate Visual Studio solution:

    ```bash
    > <installation path>\BuildSamplesSolution[DX12/VK].bat
    ```
	
	The batch file will inquire if the solution should build the SDK as a DLL (builds as a statically linked library if no (`n`) is provided) and which samples should be included.  
    Please use `1` to build a solution with all samples included or provide the list of samples to be included (using the corresponding number of the samples with spaces in between).  
    This will generate a `build\` directory where you will find the solution for the SDK samples (`FidelityFX SDK [DX12/VK] Samples.sln`).
  
<h2>Building and running in Visual Studio</h2>

<h3>Building the project</h3>

By default, the Visual Studio solution file (.sln) that is generated by CMake will contain projects for all sample applications included in the SDK. To build the projects in the solution, you should click on ``Build`` and then ``Build solution`` from the menu at the top of Visual Studio. This will build all dependencies of the samples (such as the FidelityFX Cauldron Framework), before then building the full collection of sample applications.

<h3>Running the project</h3>

To run a project from Visual Studio:

 1. Select the project of interest in the solution explorer under the "Effects" folder.

 2. Right-click the project and select 'Set as Start-up project'.

 3. Click on the `Build` menu item from the toolstrip, and then click on `Run`.
