<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   width="338.21472mm"
   height="128.8699mm"
   viewBox="0 0 338.21473 128.86991"
   version="1.1"
   id="svg1"
   inkscape:version="1.3 (0e150ed6c4, 2023-07-21)"
   sodipodi:docname="06_FSR3_UiTex.svg"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <sodipodi:namedview
     id="namedview1"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:document-units="mm"
     showguides="true"
     inkscape:lockguides="true"
     inkscape:zoom="3.2035073"
     inkscape:cx="1064.1462"
     inkscape:cy="450.28772"
     inkscape:window-width="3840"
     inkscape:window-height="2066"
     inkscape:window-x="5749"
     inkscape:window-y="301"
     inkscape:window-maximized="1"
     inkscape:current-layer="g48">
    <sodipodi:guide
       position="-48.894326,297.33038"
       orientation="0,-1"
       id="guide1"
       inkscape:locked="true" />
    <inkscape:page
       x="0"
       y="0"
       width="338.21472"
       height="128.8699"
       id="page1"
       margin="0"
       bleed="0" />
  </sodipodi:namedview>
  <defs
     id="defs1">
    <clipPath
       id="clip0">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="695325"
         id="rect1" />
    </clipPath>
    <image
       width="252"
       height="133"
       xlink:href="data:image/png;base64,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"
       preserveAspectRatio="none"
       id="img1" />
    <clipPath
       id="clip2">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="693738"
         id="rect2" />
    </clipPath>
    <image
       width="126"
       height="67"
       xlink:href="data:image/png;base64,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"
       preserveAspectRatio="none"
       id="img3" />
    <clipPath
       id="clip4">
      <rect
         x="0"
         y="0"
         width="1164325"
         height="619125"
         id="rect3" />
    </clipPath>
    <clipPath
       id="clip5">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="723900"
         id="rect4" />
    </clipPath>
    <clipPath
       id="clip6">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="693738"
         id="rect5" />
    </clipPath>
    <clipPath
       id="clip7">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect6" />
    </clipPath>
    <linearGradient
       x1="779"
       y1="166.5"
       x2="1002"
       y2="166.5"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="stroke0">
      <stop
         offset="0"
         stop-color="#FFFFFF"
         id="stop1" />
      <stop
         offset="0.87"
         stop-color="#FFFFFF"
         id="stop2" />
      <stop
         offset="0.9"
         stop-color="#007B96"
         id="stop3" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop4" />
    </linearGradient>
    <linearGradient
       x1="780"
       y1="166.5"
       x2="1001"
       y2="166.5"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="fill1">
      <stop
         offset="0"
         stop-color="#9C9EA2"
         id="stop5" />
      <stop
         offset="0.85"
         stop-color="#9C9EA2"
         id="stop6" />
      <stop
         offset="0.92"
         stop-color="#007B96"
         id="stop7" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop8" />
    </linearGradient>
    <linearGradient
       x1="669"
       y1="166.5"
       x2="942"
       y2="166.5"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="stroke0-4">
      <stop
         offset="0"
         stop-color="#FFFFFF"
         id="stop1-1" />
      <stop
         offset="0.87"
         stop-color="#FFFFFF"
         id="stop2-2" />
      <stop
         offset="0.9"
         stop-color="#007B96"
         id="stop3-8" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop4-0" />
    </linearGradient>
    <linearGradient
       x1="670"
       y1="166.5"
       x2="941"
       y2="166.5"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="fill1-9">
      <stop
         offset="0"
         stop-color="#9C9EA2"
         id="stop5-5" />
      <stop
         offset="0.85"
         stop-color="#9C9EA2"
         id="stop6-9" />
      <stop
         offset="0.92"
         stop-color="#007B96"
         id="stop7-4" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop8-7" />
    </linearGradient>
    <linearGradient
       x1="1071"
       y1="166.5"
       x2="1200"
       y2="166.5"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="stroke2">
      <stop
         offset="0"
         stop-color="#FFFFFF"
         id="stop9" />
      <stop
         offset="0.87"
         stop-color="#FFFFFF"
         id="stop10" />
      <stop
         offset="0.9"
         stop-color="#007B96"
         id="stop11" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop12" />
    </linearGradient>
    <linearGradient
       x1="1072"
       y1="166.5"
       x2="1199"
       y2="166.5"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="fill3">
      <stop
         offset="0"
         stop-color="#9C9EA2"
         id="stop13" />
      <stop
         offset="0.85"
         stop-color="#9C9EA2"
         id="stop14" />
      <stop
         offset="0.92"
         stop-color="#007B96"
         id="stop15" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop16" />
    </linearGradient>
    <linearGradient
       x1="1119.95"
       y1="396.077"
       x2="1208.05"
       y2="395.923"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="stroke4">
      <stop
         offset="0"
         stop-color="#FFFFFF"
         id="stop17" />
      <stop
         offset="0.63"
         stop-color="#FFFFFF"
         id="stop18" />
      <stop
         offset="0.84"
         stop-color="#007B96"
         id="stop19" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop20" />
    </linearGradient>
    <linearGradient
       x1="1121"
       y1="396"
       x2="1207"
       y2="396"
       gradientUnits="userSpaceOnUse"
       spreadMethod="reflect"
       id="fill5">
      <stop
         offset="0"
         stop-color="#767171"
         id="stop21" />
      <stop
         offset="0.72"
         stop-color="#767171"
         id="stop22" />
      <stop
         offset="0.9"
         stop-color="#007B96"
         id="stop23" />
      <stop
         offset="1"
         stop-color="#007B96"
         id="stop24" />
    </linearGradient>
    <clipPath
       id="clip0-4">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="695325"
         id="rect1-5" />
    </clipPath>
    <image
       width="252"
       height="133"
       xlink:href="data:image/png;base64,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"
       preserveAspectRatio="none"
       id="img1-6" />
    <clipPath
       id="clip2-3">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="693738"
         id="rect2-1" />
    </clipPath>
    <image
       width="126"
       height="67"
       xlink:href="data:image/png;base64,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"
       preserveAspectRatio="none"
       id="img3-4" />
    <clipPath
       id="clip4-2">
      <rect
         x="0"
         y="0"
         width="1164325"
         height="619125"
         id="rect3-9" />
    </clipPath>
    <clipPath
       id="clip5-6">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="723900"
         id="rect4-7" />
    </clipPath>
    <clipPath
       id="clip6-0">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="693738"
         id="rect5-4" />
    </clipPath>
    <clipPath
       id="clip7-6">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect6-8" />
    </clipPath>
    <clipPath
       id="clip0-1">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="695325"
         id="rect1-59" />
    </clipPath>
    <image
       width="252"
       height="133"
       xlink:href="data:image/png;base64,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"
       preserveAspectRatio="none"
       id="img1-9" />
    <clipPath
       id="clip2-5">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="693738"
         id="rect2-0" />
    </clipPath>
    <clipPath
       id="clip3">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="723900"
         id="rect3-4" />
    </clipPath>
    <clipPath
       id="clip4-8">
      <rect
         x="0"
         y="0"
         width="1314450"
         height="693738"
         id="rect4-6" />
    </clipPath>
    <clipPath
       id="clip5-4">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect5-8" />
    </clipPath>
    <clipPath
       id="clip0-19">
      <rect
         x="0"
         y="0"
         width="1323975"
         height="695325"
         id="rect1-2" />
    </clipPath>
    <image
       width="252"
       height="133"
       xlink:href="data:image/png;base64,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"
       preserveAspectRatio="none"
       id="img1-5" />
    <clipPath
       id="clip2-9">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect2-14" />
    </clipPath>
    <clipPath
       id="clip3-6">
      <rect
         x="0"
         y="0"
         width="1323975"
         height="695325"
         id="rect3-44" />
    </clipPath>
    <clipPath
       id="clip4-9">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect4-9" />
    </clipPath>
    <clipPath
       id="clip5-7">
      <rect
         x="0"
         y="0"
         width="1323975"
         height="695325"
         id="rect5-3" />
    </clipPath>
    <clipPath
       id="clip6-6">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect6-88" />
    </clipPath>
    <clipPath
       id="clip7-3">
      <rect
         x="0"
         y="0"
         width="1323975"
         height="695325"
         id="rect7" />
    </clipPath>
    <clipPath
       id="clip8">
      <rect
         x="0"
         y="0"
         width="1317458"
         height="695325"
         id="rect8" />
    </clipPath>
    <clipPath
       id="clip9">
      <rect
         x="0"
         y="0"
         width="1323975"
         height="723900"
         id="rect9" />
    </clipPath>
    <clipPath
       id="clip10">
      <rect
         x="0"
         y="0"
         width="1323975"
         height="698764"
         id="rect10" />
    </clipPath>
  </defs>
  <g
     inkscape:label="Ebene 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(65.74305,58.144603)">
    <g
       transform="matrix(0.26458333,0,0,0.26458333,-65.342784,-70.608987)"
       id="g48">
      <rect
         x="15"
         y="51"
         width="906"
         height="483.61221"
         fill="#eae7e8"
         id="rect11"
         style="stroke-width:0.908448" />
      <path
         d="m 156.026,319 18.995,0.243 -0.052,4 L 155.974,323 Z m 17.046,-3.782 11.922,6.153 -12.076,5.846 z"
         fill="#231e1f"
         id="path14" />
      <rect
         x="185.5"
         y="276.5"
         width="136"
         height="91"
         stroke="#231e1f"
         stroke-width="1.33333"
         stroke-miterlimit="8"
         fill="#002060"
         id="rect14" />
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(200.916,302)"
         id="text15">Pre</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(230.229,302)"
         id="text16">-</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(238.056,302)"
         id="text17">upscale</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(229.563,328)"
         id="text18">post</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(268.369,328)"
         id="text19">-</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(205.829,353)"
         id="text20">processing</text>
      <path
         d="M 0.0106619,-1.99997 18.8618,-1.89948 18.8405,2.10047 -0.0106619,1.99997 Z M 16.8832,-5.91008 28.851,0.153806 16.8192,6.08975 Z"
         fill="#231e1f"
         transform="matrix(1,0,0,-1,321,321.154)"
         id="path20" />
      <path
         d="m 512.006,322 38.201,0.122 -0.013,4 -38.2,-0.122 z m 36.214,-3.884 11.98,6.038 -12.019,5.961 z"
         fill="#231e1f"
         id="path21" />
      <rect
         x="349.5"
         y="264.5"
         width="161"
         height="114"
         stroke="#231e1f"
         stroke-width="3"
         stroke-miterlimit="8"
         fill="#ed1c24"
         id="rect21" />
      <g
         clip-path="url(#clip0-19)"
         transform="matrix(1.04987e-4,0,0,1.04987e-4,363,270)"
         id="g22">
        <g
           clip-path="url(#clip2-9)"
           transform="matrix(1.00495,0,0,1,0,-0.25)"
           id="g21">
          <use
             width="100%"
             height="100%"
             xlink:href="#img1-5"
             transform="scale(5228.01)"
             id="use21" />
        </g>
      </g>
      <text
         fill="#ffffff"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="19px"
         transform="translate(398.207,366)"
         id="text22">Upscale</text>
      <rect
         x="561.5"
         y="104.5"
         width="138"
         height="91"
         stroke="#231e1f"
         stroke-width="1.33333"
         stroke-miterlimit="8"
         fill="#002060"
         id="rect22" />
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(609.586,130)"
         id="text23">UI in</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(592.013,156)"
         id="text24">separate</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(592.433,181)"
         id="text25">resource</text>
      <rect
         x="558.5"
         y="276.5"
         width="138"
         height="91"
         stroke="#231e1f"
         stroke-width="1.33333"
         stroke-miterlimit="8"
         fill="#002060"
         id="rect25" />
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(569.852,302)"
         id="text26">Post</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(609.658,302)"
         id="text27">-</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(617.485,302)"
         id="text28">upscale</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(603.745,328)"
         id="text29">post</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(642.552,328)"
         id="text30">-</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(580.012,353)"
         id="text31">processing</text>
      <path
         d="m 696,319 h 38.35 v 4 H 696 Z m 36.35,-4 12,6 -12,6 z"
         fill="#231e1f"
         id="path31" />
      <rect
         x="21.500099"
         y="275.5"
         width="135"
         height="91"
         stroke="#231e1f"
         stroke-width="1.33333"
         stroke-miterlimit="8"
         fill="#002060"
         id="rect31" />
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="21px"
         transform="translate(56.7112,327)"
         id="text32">Render</text>
      <rect
         x="923"
         y="51.136852"
         width="348"
         height="482.73611"
         fill="#9c9ea2"
         id="rect32"
         style="stroke-width:1.16447" />
      <rect
         x="1170.5"
         y="221.5"
         width="95"
         height="88"
         stroke="#231e1f"
         stroke-width="1.33333"
         stroke-miterlimit="8"
         fill="#002060"
         id="rect33" />
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="15px"
         transform="translate(1205.58,261)"
         id="text33">Real</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="15px"
         transform="translate(1194.67,279)"
         id="text34">Present</text>
      <path
         d="M 0.03629,-1.99967 14.1269,-1.74396 14.0543,2.25539 -0.03629,1.99967 Z M 12.1998,-5.77959 24.0889,0.437165 11.982,6.21844 Z"
         fill="#231e1f"
         transform="matrix(1,0,0,-1,1146,265.437)"
         id="path34" />
      <rect
         x="1159.5"
         y="405.5"
         width="95"
         height="88"
         stroke="#231e1f"
         stroke-width="1.33333"
         stroke-miterlimit="8"
         fill="#002060"
         id="rect34" />
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="15px"
         transform="translate(1169.52,444)"
         id="text35">Interpolated</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Medium', 'Klavika Medium_MSFontService', sans-serif"
         font-weight="500"
         font-size="15px"
         transform="translate(1183.42,462)"
         id="text36">Present</text>
      <path
         d="m 1118,447 31.05,0.072 -0.01,4 L 1118,451 Z m 29.06,-3.933 11.98,6.028 -12.01,5.972 z"
         fill="#231e1f"
         id="path36" />
      <rect
         x="746.5"
         y="392.5"
         width="160"
         height="113"
         stroke="#231e1f"
         stroke-width="3"
         stroke-miterlimit="8"
         fill="#ed1c24"
         id="rect36" />
      <g
         clip-path="url(#clip3-6)"
         transform="matrix(1.04987e-4,0,0,1.04987e-4,759,398)"
         id="g37">
        <g
           clip-path="url(#clip4-9)"
           transform="matrix(1.00495,0,0,1,-0.5,0)"
           id="g36">
          <use
             width="100%"
             height="100%"
             xlink:href="#img1-5"
             transform="scale(5228.01)"
             id="use36" />
        </g>
      </g>
      <text
         fill="#ffffff"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="19px"
         transform="translate(758.781,494)"
         id="text37">Frame generation</text>
      <text
         fill="#231e1f"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="19px"
         id="text38"
         x="944.99371"
         y="93.612221">FSR 3 Async frame pacing thread</text>
      <rect
         x="986.5"
         y="209.5"
         width="160"
         height="113"
         stroke="#231e1f"
         stroke-width="3"
         stroke-miterlimit="8"
         fill="#ed1c24"
         id="rect38" />
      <g
         clip-path="url(#clip5-7)"
         transform="matrix(1.04987e-4,0,0,1.04987e-4,999,215)"
         id="g39">
        <g
           clip-path="url(#clip6-6)"
           transform="matrix(1.00495,0,0,1,-1,-0.125)"
           id="g38">
          <use
             width="100%"
             height="100%"
             xlink:href="#img1-5"
             transform="scale(5228.01)"
             id="use38" />
        </g>
      </g>
      <text
         fill="#ffffff"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="19px"
         transform="translate(1000.17,311)"
         id="text39">Composition pass</text>
      <rect
         x="957.5"
         y="392.5"
         width="161"
         height="113"
         stroke="#231e1f"
         stroke-width="3"
         stroke-miterlimit="8"
         fill="#ed1c24"
         id="rect39" />
      <g
         clip-path="url(#clip7-3)"
         transform="matrix(1.04987e-4,0,0,1.04987e-4,970,398)"
         id="g41">
        <g
           clip-path="url(#clip8)"
           transform="matrix(1.00495,0,0,1,-1,0)"
           id="g40">
          <use
             width="100%"
             height="100%"
             xlink:href="#img1-5"
             transform="scale(5228.01)"
             id="use39" />
        </g>
      </g>
      <text
         fill="#ffffff"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="19px"
         transform="translate(971.482,494)"
         id="text41">Composition pass</text>
      <path
         d="m 906,447 h 40.449 v 4 H 906 Z m 38.449,-4 12,6 -12,6 z"
         fill="#231e1f"
         id="path41" />
      <path
         d="m 906,264 h 69.137 v 4 H 906 Z m 67.137,-4 12,6 -12,6 z"
         fill="#231e1f"
         id="path42" />
      <rect
         x="745.5"
         y="201.5"
         width="161"
         height="141"
         stroke="#231e1f"
         stroke-width="3"
         stroke-miterlimit="8"
         fill="#ed1c24"
         id="rect42" />
      <g
         clip-path="url(#clip9)"
         transform="matrix(1.04987e-4,0,0,1.04987e-4,759,208)"
         id="g43">
        <g
           clip-path="url(#clip10)"
           transform="matrix(1,0,0,1.03597,-0.5,-0.125)"
           id="g42">
          <use
             width="100%"
             height="100%"
             xlink:href="#img1-5"
             transform="scale(5253.87)"
             id="use42" />
        </g>
      </g>
      <text
         fill="#ffffff"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="16px"
         transform="translate(779.92,307)"
         id="text43">Interpolation</text>
      <text
         fill="#ffffff"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="16px"
         transform="translate(787.253,327)"
         id="text44">Swapchain</text>
      <path
         d="m 828,341.986 0.289,40.115 -4,0.029 L 824,342.014 Z m 4.275,38.087 -5.913,12.042 -6.087,-11.956 z"
         fill="#231e1f"
         id="path44" />
      <text
         fill="#231e1f"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="24px"
         transform="translate(405.979,456)"
         id="text45">Same UI is composited onto</text>
      <text
         fill="#231e1f"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="24px"
         transform="translate(405.979,484)"
         id="text46">real and generated frames.</text>
      <text
         fill="#231e1f"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="24px"
         transform="translate(405.979,513)"
         id="text47">UI runs at render rate.</text>
      <path
         d="m 699,148.167 h 265.687 v 81.464 l -0.834,-0.834 h 14.988 v 1.667 H 963.02 V 149 l 0.833,0.833 H 699 Z m 278.507,77.464 8,4 -8,4 z"
         fill="#231e1f"
         id="path47" />
      <path
         d="m 965.167,230 v 155.339 h -2.334 V 230 Z m 2.833,154.006 -4,8 -4,-8 z"
         fill="#231e1f"
         id="path48" />
      <text
         fill="#231e1f"
         font-family="'Klavika Regular', 'Klavika Regular_MSFontService', sans-serif"
         font-weight="400"
         font-size="19px"
         transform="translate(39.596,72)"
         id="text48">Application thread calling swap chain present()</text>
    </g>
  </g>
</svg>
