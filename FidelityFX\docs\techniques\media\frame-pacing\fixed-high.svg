<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="50 0 400 480" style="background:white; font-size:10">
    <!-- Hand written by OH -->
    <defs>
        <marker id="arrow-pointy" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="10" markerHeight="10"
            orient="auto-start-reverse">
            <path d="M 1 1 9 5 1 9" stroke="black" fill="none" stroke-linecap="round" />
        </marker>
        <marker id="arrow" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="10" markerHeight="10"
            orient="auto-start-reverse">
            <path d="M 1 1 9 5 1 9" stroke="black" fill="none" stroke-linecap="round" />
    </marker>
    </defs>
    <text x="100" y="20" text-anchor="middle">Game</text>
    <text x="200" y="20" text-anchor="middle">FSR3 (simplified)</text>
    <text x="300" y="20" text-anchor="middle">GPU</text>
    <path d="M100,75v10m0,90v10m0,90v10m0,90v10m0,90v10M200,30V500M300,30V500" stroke="black" />
    <path d="M97 30 v45h6v-45 a 3 3 180 0 1 -3 0 a 3 3 180 0 0 -3 0" stroke="black" fill="none" />
    <g id="frame-arrows">
        <rect x="97" y="85" width="6" height="90" fill="white" stroke="black" />
        <text text-anchor="middle" x="150" y="75">Present()</text>
        <path d="M100 80 h100" marker-end="url(#arrow)" stroke="black" />
        <text text-anchor="middle" x="250" y="85">Present int.</text>
        <text text-anchor="middle" x="250" y="135">Present real</text>
        <path d="M200 90 h100" marker-end="url(#arrow)" stroke="black" />
        <path d="M200 140 h100" marker-end="url(#arrow)" stroke="black" />

        <path d="M310 90 h40" marker-end="url(#arrow-pointy)" stroke="black" />
        <text x="352" y="93">Display</text>
        <path d="M310 140 h40" marker-end="url(#arrow-pointy)" stroke="black" />
        <text x="352" y="143">Display</text>
    </g>
    <use href="#frame-arrows" y="100" />
    <use href="#frame-arrows" y="200" />
    <use href="#frame-arrows" y="300" />
    <text x="402" y="77">VSync</text>
    <path id="vsync-arrow" d="M400 40 v60" marker-start="url(#arrow)" marker-end="url(#arrow)" stroke="black" />
    <use href="#vsync-arrow" y="60" />
    <use href="#vsync-arrow" y="120" />
    <use href="#vsync-arrow" y="180" />
    <use href="#vsync-arrow" y="240" />
    <use href="#vsync-arrow" y="300" />
    <use href="#vsync-arrow" y="300" />
    <use href="#vsync-arrow" y="360" />
    <use href="#vsync-arrow" y="420" />
</svg>