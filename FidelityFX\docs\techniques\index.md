<!-- @page page_techniques_index Techniques -->

<h1>Techniques</h1>

- [FidelityFX Blur 1.1](blur.md)
- [FidelityFX Breadcrumbs 1.0.1](breadcrumbs.md)
- [FidelityFX Brixelizer 1.0](brixelizer.md)
- [FidelityFX Brixelizer GI 1.0.1](brixelizer-gi.md)
- [FidelityFX Classifier 1.3](classifier.md)
- [FidelityFX Combined Adaptive Compute Ambient Occlusion 1.4](combined-adaptive-compute-ambient-occlusion.md)
- [FidelityFX Contrast Adaptive Sharpening 1.2](contrast-adaptive-sharpening.md)
- [FidelityFX Denoiser 1.3](denoiser.md)
- [FidelityFX Depth of Field 1.1](depth-of-field.md)
- [FidelityFX Frame Interpolation 1.1.3](frame-interpolation.md)
- [FidelityFX Frame Interpolation Swapchain 1.1.3](frame-interpolation-swap-chain.md)
- [FidelityFX Lens 1.1](lens.md)
- [FidelityFX Luminance Preserving Mapper 1.4](luminance-preserving-mapper.md)
- [FidelityFX Optical Flow 1.1.2](optical-flow.md)
- [FidelityFX Parallel Sort 1.3](parallel-sort.md)
- [FidelityFX Single Pass Downsampler 2.2](single-pass-downsampler.md)
- [FidelityFX Stochastic Screen-Space Reflections 1.5](stochastic-screen-space-reflections.md)
- [FidelityFX Super Resolution 1.2](super-resolution-spatial.md)
- [FidelityFX Super Resolution 2.3.3](super-resolution-temporal.md)
- [FidelityFX Super Resolution 3.1.4 - Upscaling and Frame Generation](super-resolution-interpolation.md)
- [FidelityFX Super Resolution 3.1.4 - Upscaler](super-resolution-upscaler.md)
- [FidelityFX Variable Shading 1.2](variable-shading.md)

<!-- - @subpage page_techniques_combined-adaptive-compute-ambient-occlusion "Combined Adaptive Compute Ambient Occlusion" -->
<!-- - @subpage page_techniques_contrast-adaptive-sharpening "Contrast Adaptive Sharpening" -->
<!-- - @subpage page_techniques_denoiser "Denoiser" -->
<!-- - @subpage page_techniques_classifier "Classifer" -->
<!-- - @subpage page_techniques_lpm "Luminance Preserving Mapper" -->
<!-- - @subpage page_techniques_parallel-sort "Parallel Sort" -->
<!-- - @subpage page_techniques_single-pass-downsampler "Single Pass Downsampler" -->
<!-- - @subpage page_techniques_stochastic-screen-space-reflections "Stochastic Screen Space Reflections" -->
<!-- - @subpage page_techniques_super-resolution-spatial "Super Resolution (Spatial)" -->
<!-- - @subpage page_techniques_super-resolution-temporal "Super Resolution (Temporal)" -->
<!-- - @subpage page_techniques_super-resolution-interpolation "Super Resolution (Interpolation)" -->
<!-- - @subpage page_techniques_super-resolution-upscaler "Super Resolution (Upscaler)" -->
<!-- - @subpage page_techniques_frame-interpolation "Frame Interpolation" -->
<!-- - @subpage page_techniques_frame-interpolation-swap-chain "Frame Interpolation Swapchain" -->
<!-- - @subpage page_techniques_optical-flow "Optical Flow" -->
<!-- - @subpage page_techniques_variable-shading "Variable Shading" -->
<!-- - @subpage page_techniques_blur "Blur" -->
<!-- - @subpage page_techniques_depth-of-field "Depth of Field" -->
<!-- - @subpage page_techniques_lens "Lens" -->
<!-- - @subpage page_techniques_breadcrumbs "Breadcrumbs" -->
<!-- - @subpage page_techniques_brixelizer "Brixelizer" -->
<!-- - @subpage page_techniques_brixelizer_gi "Brixelizer GI" -->
