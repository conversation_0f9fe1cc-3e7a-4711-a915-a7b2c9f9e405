<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="50 0 350 480" style="background:white; font-size:10">
    <!-- Hand written by OH -->
    <defs>
        <marker id="arrow-pointy" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="10" markerHeight="10"
            orient="auto-start-reverse">
            <path d="M 1 1 9 5 1 9" stroke="black" fill="none" stroke-linecap="round" />
        </marker>
        <marker id="arrow" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="10" markerHeight="10"
            orient="auto-start-reverse">
            <path d="M 1 1 9 5 1 9" stroke="black" fill="none" stroke-linecap="round" />
    </marker>
    </defs>
    <text x="100" y="20" text-anchor="middle">Game</text>
    <text x="200" y="20" text-anchor="middle">FSR3 (simplified)</text>
    <text x="300" y="20" text-anchor="middle">GPU</text>
    <path d="M100,75v10m0,130v10m0,130v10M200,30V500M300,30V500" stroke="black" />
    <path d="M97 30 v45h6v-45 a 3 3 180 0 1 -3 0 a 3 3 180 0 0 -3 0" stroke="black" fill="none" />
    <g id="frame-arrows">
        <rect x="97" y="85" width="6" height="130" fill="white" stroke="black" />
        <text text-anchor="middle" x="150" y="75">Present()</text>
        <path d="M100 80 h100" marker-end="url(#arrow)" stroke="black" />
        <text text-anchor="middle" x="250" y="90">Present int.</text>
        <text text-anchor="middle" x="250" y="160">Present real</text>
        <path d="M200 95 h100" marker-end="url(#arrow)" stroke="black" />
        <path d="M200 165 h100" marker-end="url(#arrow)" stroke="black" />
    </g>
    <use href="#frame-arrows" y="140" />
    <use href="#frame-arrows" y="280" />
    <g id="vsync-arrow">
        <path d="M310 40 h40" marker-end="url(#arrow-pointy)" stroke="black" />
        <path d="M330 40 v60" marker-end="url(#arrow-pointy)" stroke="black" />
        <text x="332" y="77">Scan out</text>
        <text x="352" y="43">Display</text>
    </g>
    <text text-anchor="middle" x="250" y="35">Present real</text>
    <path d="M200 40 h100" marker-end="url(#arrow)" stroke="black" />
    <use href="#vsync-arrow" y="60" />
    <use href="#vsync-arrow" y="125" />
    <use href="#vsync-arrow" y="195" />
    <use href="#vsync-arrow" y="265" />
    <use href="#vsync-arrow" y="335" />
    <use href="#vsync-arrow" y="405" />
</svg>